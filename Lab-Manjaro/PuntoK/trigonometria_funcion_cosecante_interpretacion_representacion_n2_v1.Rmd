---
output:
  html_document: default
  word_document: default
  pdf_document: default
---
```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{amsmath}",
  "\\usepackage{array}",
  "\\usepackage{xcolor}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)
library(knitr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Establecer semilla aleatoria
set.seed(sample(1:10000, 1))

# Función principal de generación de datos
generar_datos <- function() {
  # Aleatorización de parámetros principales
  h_valores <- c(3, 4, 5, 6)
  h <- sample(h_valores, 1)
  
  # Aleatorización de nombres de puntos
  nombres_punto_movil <- c("K", "P", "M", "N", "R")
  nombres_punto_fijo <- c("Q", "S", "T", "U", "V")
  punto_movil <- sample(nombres_punto_movil, 1)
  punto_fijo1 <- sample(nombres_punto_fijo, 1)
  punto_fijo2 <- sample(setdiff(nombres_punto_fijo, punto_fijo1), 1)
  
  # Aleatorización de términos matemáticos
  terminos_distancia <- c("distancia", "longitud", "medida", "segmento")
  termino_distancia <- sample(terminos_distancia, 1)
  
  terminos_grafica <- c("gráfica", "gráfico", "diagrama", "representación")
  termino_grafica <- sample(terminos_grafica, 1)
  
  # Aleatorización de colores para las gráficas
  colores_disponibles <- c("blue", "red", "green", "purple", "orange", "brown", "pink", "gray")
  colores_graficas <- sample(colores_disponibles, 4, replace = FALSE)
  
  # Rango de ángulos (evitar 0° y 90° para evitar problemas matemáticos)
  angulo_min <- 5
  angulo_max <- 85
  
  # Generar las opciones de respuesta
  # Opción correcta: KP = h/sen(α)
  # Distractores: diferentes funciones
  
  # Aleatorizar qué opción (A, B, C, D) será la correcta
  letras <- c("A", "B", "C", "D")
  letra_correcta <- sample(letras, 1)
  
  # Crear mapeo de funciones a letras
  funciones <- list(
    correcta = "cosecante",  # KP = h/sen(α)
    constante = "constante", # KP = h (línea horizontal)
    lineal = "lineal",       # KP = función lineal decreciente
    cuadratica = "cuadratica" # KP = función cuadrática
  )
  
  # Asignar funciones a letras aleatoriamente
  funciones_mezcladas <- sample(names(funciones), 4)
  mapeo_funciones <- setNames(funciones_mezcladas, letras)
  
  return(list(
    h = h,
    punto_movil = punto_movil,
    punto_fijo1 = punto_fijo1,
    punto_fijo2 = punto_fijo2,
    termino_distancia = termino_distancia,
    termino_grafica = termino_grafica,
    colores_graficas = colores_graficas,
    angulo_min = angulo_min,
    angulo_max = angulo_max,
    letra_correcta = letra_correcta,
    mapeo_funciones = mapeo_funciones
  ))
}

# Generar datos del ejercicio
datos <- generar_datos()

# Extraer variables individuales
h <- datos$h
punto_movil <- datos$punto_movil
punto_fijo1 <- datos$punto_fijo1
punto_fijo2 <- datos$punto_fijo2
termino_distancia <- datos$termino_distancia
termino_grafica <- datos$termino_grafica
colores_graficas <- datos$colores_graficas
angulo_min <- datos$angulo_min
angulo_max <- datos$angulo_max
letra_correcta <- datos$letra_correcta
mapeo_funciones <- datos$mapeo_funciones

# Crear vector de solución
solucion <- rep(0, 4)
indice_correcto <- which(names(mapeo_funciones) == letra_correcta)
solucion[indice_correcto] <- 1
```

```{r version_diversity_test, echo=FALSE, results="hide"}
# Prueba obligatoria de diversidad de versiones
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:1000) {
    datos_test <- generar_datos()
    versiones[[i]] <- digest::digest(datos_test)
  }
  
  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 300,
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones únicas. Se requieren al menos 300."))
})
```

```{r generar_graficas_python, echo=FALSE, results="hide"}
# Generar las cuatro gráficas usando Python
for (i in 1:4) {
  letra <- names(mapeo_funciones)[i]
  tipo_funcion <- mapeo_funciones[[letra]]
  color <- colores_graficas[i]
  letra_lower <- tolower(letra)
  
  # Formatear valores con control preciso
  old_locale <- Sys.getlocale("LC_NUMERIC")
  Sys.setlocale("LC_NUMERIC", "C")
  
  h_fmt <- sprintf("%.1f", h)
  angulo_min_fmt <- sprintf("%.1f", angulo_min)
  angulo_max_fmt <- sprintf("%.1f", angulo_max)
  
  # Restaurar locale original
  Sys.setlocale("LC_NUMERIC", old_locale)
  
  # Código Python para generar cada gráfica
  codigo_grafica <- paste0("
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np

plt.style.use('default')
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.2

# Parámetros
h = ", h_fmt, "
angulo_min = ", angulo_min_fmt, "
angulo_max = ", angulo_max_fmt, "

# Generar ángulos en radianes
angulos_deg = np.linspace(angulo_min, angulo_max, 100)
angulos_rad = np.radians(angulos_deg)

# Calcular KP según el tipo de función
if '", tipo_funcion, "' == 'correcta':
    # KP = h/sen(α) - función cosecante
    KP = h / np.sin(angulos_rad)
elif '", tipo_funcion, "' == 'constante':
    # KP = h (constante)
    KP = np.full_like(angulos_deg, h)
elif '", tipo_funcion, "' == 'lineal':
    # KP = función lineal decreciente
    KP = h * 2 - (angulos_deg - angulo_min) * h / (angulo_max - angulo_min)
elif '", tipo_funcion, "' == 'cuadratica':
    # KP = función cuadrática con pico inicial
    x_norm = (angulos_deg - angulo_min) / (angulo_max - angulo_min)
    KP = h * (2 + 3 * np.exp(-5 * x_norm))

fig, ax = plt.subplots(1, 1, figsize=(6, 4))

# Dibujar la función
ax.plot(angulos_deg, KP, color='", color, "', linewidth=2.5)

# Configurar ejes y etiquetas
ax.set_xlim(angulo_min, angulo_max)
if '", tipo_funcion, "' == 'correcta':
    ax.set_ylim(h, h * 3)
else:
    ax.set_ylim(0, h * 3)

ax.set_xlabel('Ángulo α (grados)', fontsize=12)
ax.set_ylabel('Distancia ", punto_movil, "P', fontsize=12)
ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.8, color='gray')

# Configurar ticks
ax.set_xticks(np.arange(angulo_min, angulo_max + 1, 15))
ax.set_yticks(np.arange(0, h * 3 + 1, h))

plt.tight_layout()

plt.savefig('grafica_", letra_lower, ".png', dpi=150, bbox_inches='tight',
           facecolor='white', edgecolor='none')
plt.savefig('grafica_", letra_lower, ".pdf', dpi=150, bbox_inches='tight',
           facecolor='white', edgecolor='none')
plt.close()
")
  
  py_run_string(codigo_grafica)
}
```

```{r generar_tikz_diagrama, echo=FALSE, results="asis"}
# Generar el diagrama geométrico principal usando TikZ
generar_tikz_diagrama <- function(datos) {
  tikz_code <- paste0(
    "\\begin{tikzpicture}[scale=1.0]\n",

    "% Dibujar el segmento principal\n",
    "\\draw[thick] (0, 0) -- (8, 0);\n",
    "\\fill[black] (0, 0) circle (0.08);\n",
    "\\fill[black] (8, 0) circle (0.08);\n",
    "\\node[below] at (0, -0.3) {", datos$punto_fijo1, "};\n",
    "\\node[below] at (8, -0.3) {", datos$punto_fijo2, "};\n\n",

    "% Punto movil sobre el segmento\n",
    "\\fill[black] (5, 0) circle (0.08);\n",
    "\\node[below] at (5, -0.3) {", datos$punto_movil, "};\n\n",

    "% Punto P arriba\n",
    "\\fill[black] (5, 3) circle (0.08);\n",
    "\\node[above] at (5, 3.3) {P};\n\n",

    "% Lineas del triangulo\n",
    "\\draw[thick] (5, 0) -- (5, 3);\n",
    "\\draw[thick, dashed] (0, 0) -- (5, 3);\n\n",

    "% Marcar altura h\n",
    "\\node[right] at (5.2, 1.5) {h};\n\n",

    "% Marcar angulo\n",
    "\\draw[thick] (0.8, 0) arc (0:30:0.8);\n",
    "\\node at (1.2, 0.3) {$\\alpha$};\n\n",

    "% Texto explicativo\n",
    "\\node[anchor=north west, text width=10cm] at (0, -1) {\n",
    "  \\textbf{El angulo y la medida h se relacionan mediante}\n",
    "};\n",
    "\\node[anchor=north west, text width=10cm] at (0, -1.8) {\n",
    "  \\textbf{la razon trigonometrica donde la ", datos$termino_distancia, " ", datos$punto_movil, "P}\n",
    "};\n",
    "\\node[anchor=north west, text width=10cm] at (0, -2.6) {\n",
    "  \\textbf{varia segun el angulo.}\n",
    "};\n\n",

    "\\end{tikzpicture}"
  )

  return(tikz_code)
}

# Generar el código TikZ final
tikz_final <- generar_tikz_diagrama(datos)
```

Question
========

Un punto `r punto_movil` se mueve de un extremo a otro del segmento `r punto_fijo1``r punto_fijo2` que se muestra en la gráfica.

```{r figura_trigonometrica, echo=FALSE, results='asis'}
include_tikz(tikz_final,
             name = "diagrama_trigonometrico",
             markup = "markdown",
             format = typ,
             packages = c("tikz", "colortbl", "amsmath", "array"),
             width = "12cm")
```

¿Cuál de las siguientes `r termino_grafica`s representa mejor la relación entre la `r termino_distancia` `r punto_movil`P y el ángulo α?

Answerlist
----------

```{r mostrar_opciones_graficas, echo=FALSE, results='asis'}
# Detectar formato de salida
formatos_moodle <- c("exams2moodle", "exams2qti12", "exams2qti21", "exams2openolat")
es_moodle <- (match_exams_call() %in% formatos_moodle)

# Mostrar las opciones con sus respectivas imágenes siguiendo el patrón de ejemplos funcionales
cat("-\n")
if (es_moodle) {
  cat("![](grafica_a.png){width=60%}\n\n")
} else {
  cat("![](grafica_a.png){width=70%}\n\n")
}

cat("-\n")
if (es_moodle) {
  cat("![](grafica_b.png){width=60%}\n\n")
} else {
  cat("![](grafica_b.png){width=70%}\n\n")
}

cat("-\n")
if (es_moodle) {
  cat("![](grafica_c.png){width=60%}\n\n")
} else {
  cat("![](grafica_c.png){width=70%}\n\n")
}

cat("-\n")
if (es_moodle) {
  cat("![](grafica_d.png){width=60%}\n\n")
} else {
  cat("![](grafica_d.png){width=70%}\n\n")
}
```

Solution
========

Para resolver este problema, debemos analizar la relación trigonométrica dada y determinar cómo se comporta la función `r punto_movil`P = h/sen(α) cuando el ángulo α varía.

### Paso 1: Análisis de la relación trigonométrica

La relación dada es:
$$\sin(\alpha) = \frac{h}{`r punto_movil`P}$$

Despejando `r punto_movil`P:
$$`r punto_movil`P = \frac{h}{\sin(\alpha)} = h \times \csc(\alpha)$$

Donde:
- h es una constante (altura fija = `r h`)
- α es el ángulo variable
- `r punto_movil`P es la `r termino_distancia` que queremos analizar

### Paso 2: Comportamiento de la función cosecante

La función cosecante tiene las siguientes características importantes:

1. **Cuando α → 0°**: sin(α) → 0, por lo tanto `r punto_movil`P = h/sin(α) → ∞
2. **Cuando α aumenta**: sin(α) aumenta, por lo tanto `r punto_movil`P = h/sin(α) disminuye
3. **Cuando α → 90°**: sin(α) → 1, por lo tanto `r punto_movil`P = h/sin(α) → h = `r h`

### Paso 3: Características de la gráfica correcta

La gráfica que representa correctamente `r punto_movil`P = h/sin(α) debe mostrar:

- **Función decreciente**: A medida que α aumenta, `r punto_movil`P disminuye
- **Comportamiento asintótico**: Para ángulos pequeños, `r punto_movil`P tiende a valores muy grandes
- **Valor mínimo**: Cuando α se acerca a 90°, `r punto_movil`P se acerca a h = `r h`
- **Forma de cosecante**: Curva suave y decreciente, no lineal

### Paso 4: Análisis de las opciones

```{r mostrar_grafica_correcta, echo=FALSE, results='asis'}
# Mostrar la gráfica correcta en la solución
letra_correcta_lower <- tolower(letra_correcta)
nombre_archivo <- paste0("grafica_", letra_correcta_lower, ".png")
cat("**La gráfica correcta es la opción", letra_correcta, ":**\n\n")
if (es_moodle) {
  cat("![](", nombre_archivo, "){width=60%}\n\n")
} else {
  cat("![](", nombre_archivo, "){width=70%}\n\n")
}
```

Esta gráfica es correcta porque:

1. **Muestra una función decreciente**: Conforme α aumenta, `r punto_movil`P disminuye
2. **Tiene la forma característica de la cosecante**: Curva suave que decrece rápidamente al inicio y luego se estabiliza
3. **Se aproxima al valor mínimo h**: Para ángulos cercanos a 90°, `r punto_movil`P se acerca a `r h`
4. **Es matemáticamente consistente**: Representa fielmente la función `r punto_movil`P = `r h`/sin(α)

### Paso 5: ¿Por qué las otras opciones son incorrectas?

- **Gráficas horizontales constantes**: No representan la variación de `r punto_movil`P con respecto a α
- **Funciones lineales**: La relación cosecante no es lineal
- **Funciones con comportamiento incorrecto**: No siguen el patrón matemático de h/sen(α)

### Conclusión

La función `r punto_movil`P = h/sin(α) = h × csc(α) es una función cosecante que decrece desde valores muy altos (cuando α es pequeño) hasta el valor mínimo h (cuando α se acerca a 90°). Solo la opción **`r letra_correcta`** representa correctamente este comportamiento matemático.

Answerlist
----------
- `r if(solucion[1] == 1) "Verdadero" else "Falso"`
- `r if(solucion[2] == 1) "Verdadero" else "Falso"`
- `r if(solucion[3] == 1) "Verdadero" else "Falso"`
- `r if(solucion[4] == 1) "Verdadero" else "Falso"`

Meta-information
================
exname: trigonometria_funcion_cosecante_interpretacion_representacion_n2_v1
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
exsection: Trigonometría|Funciones trigonométricas|Interpretación de gráficos
exextra[Type]: Interpretación y representación
exextra[Level]: 2
exextra[Language]: es
exextra[Course]: Matemáticas ICFES
