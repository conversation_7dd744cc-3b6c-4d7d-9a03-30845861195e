% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
%
\documentclass[
]{article}
\usepackage{amsmath,amssymb}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{xcolor}
\usepackage[margin=1in]{geometry}
\usepackage{color}
\usepackage{fancyvrb}
\newcommand{\VerbBar}{|}
\newcommand{\VERB}{\Verb[commandchars=\\\{\}]}
\DefineVerbatimEnvironment{Highlighting}{Verbatim}{commandchars=\\\{\}}
% Add ',fontsize=\small' for more characters per line
\usepackage{framed}
\definecolor{shadecolor}{RGB}{248,248,248}
\newenvironment{Shaded}{\begin{snugshade}}{\end{snugshade}}
\newcommand{\AlertTok}[1]{\textcolor[rgb]{0.94,0.16,0.16}{#1}}
\newcommand{\AnnotationTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\AttributeTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{#1}}
\newcommand{\BaseNTok}[1]{\textcolor[rgb]{0.00,0.00,0.81}{#1}}
\newcommand{\BuiltInTok}[1]{#1}
\newcommand{\CharTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\CommentTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textit{#1}}}
\newcommand{\CommentVarTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\ConstantTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{#1}}
\newcommand{\ControlFlowTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{\textbf{#1}}}
\newcommand{\DataTypeTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{#1}}
\newcommand{\DecValTok}[1]{\textcolor[rgb]{0.00,0.00,0.81}{#1}}
\newcommand{\DocumentationTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\ErrorTok}[1]{\textcolor[rgb]{0.64,0.00,0.00}{\textbf{#1}}}
\newcommand{\ExtensionTok}[1]{#1}
\newcommand{\FloatTok}[1]{\textcolor[rgb]{0.00,0.00,0.81}{#1}}
\newcommand{\FunctionTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{\textbf{#1}}}
\newcommand{\ImportTok}[1]{#1}
\newcommand{\InformationTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\KeywordTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{\textbf{#1}}}
\newcommand{\NormalTok}[1]{#1}
\newcommand{\OperatorTok}[1]{\textcolor[rgb]{0.81,0.36,0.00}{\textbf{#1}}}
\newcommand{\OtherTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{#1}}
\newcommand{\PreprocessorTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textit{#1}}}
\newcommand{\RegionMarkerTok}[1]{#1}
\newcommand{\SpecialCharTok}[1]{\textcolor[rgb]{0.81,0.36,0.00}{\textbf{#1}}}
\newcommand{\SpecialStringTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\StringTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\VariableTok}[1]{\textcolor[rgb]{0.00,0.00,0.00}{#1}}
\newcommand{\VerbatimStringTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\WarningTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\usepackage{graphicx}
\makeatletter
\newsavebox\pandoc@box
\newcommand*\pandocbounded[1]{% scales image to fit in text height/width
  \sbox\pandoc@box{#1}%
  \Gscale@div\@tempa{\textheight}{\dimexpr\ht\pandoc@box+\dp\pandoc@box\relax}%
  \Gscale@div\@tempb{\linewidth}{\wd\pandoc@box}%
  \ifdim\@tempb\p@<\@tempa\p@\let\@tempa\@tempb\fi% select the smaller of both
  \ifdim\@tempa\p@<\p@\scalebox{\@tempa}{\usebox\pandoc@box}%
  \else\usebox{\pandoc@box}%
  \fi%
}
% Set default figure placement to htbp
\def\fps@figure{htbp}
\makeatother
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  hidelinks,
  pdfcreator={LaTeX via pandoc}}

\author{}
\date{\vspace{-2.5em}}

\begin{document}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{options}\NormalTok{(}\AttributeTok{OutDec =} \StringTok{"."}\NormalTok{)  }\CommentTok{\# Asegurar punto decimal en este chunk}

\CommentTok{\# Establecer semilla aleatoria}
\FunctionTok{set.seed}\NormalTok{(}\FunctionTok{sample}\NormalTok{(}\DecValTok{1}\SpecialCharTok{:}\DecValTok{10000}\NormalTok{, }\DecValTok{1}\NormalTok{))}

\CommentTok{\# Función principal de generación de datos}
\NormalTok{generar\_datos }\OtherTok{\textless{}{-}} \ControlFlowTok{function}\NormalTok{() \{}
  \CommentTok{\# Aleatorización de parámetros principales}
\NormalTok{  h\_valores }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\DecValTok{3}\NormalTok{, }\DecValTok{4}\NormalTok{, }\DecValTok{5}\NormalTok{, }\DecValTok{6}\NormalTok{)}
\NormalTok{  h }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(h\_valores, }\DecValTok{1}\NormalTok{)}
  
  \CommentTok{\# Aleatorización de nombres de puntos}
\NormalTok{  nombres\_punto\_movil }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"K"}\NormalTok{, }\StringTok{"P"}\NormalTok{, }\StringTok{"M"}\NormalTok{, }\StringTok{"N"}\NormalTok{, }\StringTok{"R"}\NormalTok{)}
\NormalTok{  nombres\_punto\_fijo }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"Q"}\NormalTok{, }\StringTok{"S"}\NormalTok{, }\StringTok{"T"}\NormalTok{, }\StringTok{"U"}\NormalTok{, }\StringTok{"V"}\NormalTok{)}
\NormalTok{  punto\_movil }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(nombres\_punto\_movil, }\DecValTok{1}\NormalTok{)}
\NormalTok{  punto\_fijo1 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(nombres\_punto\_fijo, }\DecValTok{1}\NormalTok{)}
\NormalTok{  punto\_fijo2 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\FunctionTok{setdiff}\NormalTok{(nombres\_punto\_fijo, punto\_fijo1), }\DecValTok{1}\NormalTok{)}
  
  \CommentTok{\# Aleatorización de términos matemáticos}
\NormalTok{  terminos\_distancia }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"distancia"}\NormalTok{, }\StringTok{"longitud"}\NormalTok{, }\StringTok{"medida"}\NormalTok{, }\StringTok{"segmento"}\NormalTok{)}
\NormalTok{  termino\_distancia }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(terminos\_distancia, }\DecValTok{1}\NormalTok{)}
  
\NormalTok{  terminos\_grafica }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"gráfica"}\NormalTok{, }\StringTok{"gráfico"}\NormalTok{, }\StringTok{"diagrama"}\NormalTok{, }\StringTok{"representación"}\NormalTok{)}
\NormalTok{  termino\_grafica }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(terminos\_grafica, }\DecValTok{1}\NormalTok{)}
  
  \CommentTok{\# Aleatorización de colores para las gráficas}
\NormalTok{  colores\_disponibles }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"blue"}\NormalTok{, }\StringTok{"red"}\NormalTok{, }\StringTok{"green"}\NormalTok{, }\StringTok{"purple"}\NormalTok{, }\StringTok{"orange"}\NormalTok{, }\StringTok{"brown"}\NormalTok{, }\StringTok{"pink"}\NormalTok{, }\StringTok{"gray"}\NormalTok{)}
\NormalTok{  colores\_graficas }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(colores\_disponibles, }\DecValTok{4}\NormalTok{, }\AttributeTok{replace =} \ConstantTok{FALSE}\NormalTok{)}
  
  \CommentTok{\# Rango de ángulos (evitar 0° y 90° para evitar problemas matemáticos)}
\NormalTok{  angulo\_min }\OtherTok{\textless{}{-}} \DecValTok{5}
\NormalTok{  angulo\_max }\OtherTok{\textless{}{-}} \DecValTok{85}
  
  \CommentTok{\# Generar las opciones de respuesta}
  \CommentTok{\# Opción correcta: KP = h/sen(α)}
  \CommentTok{\# Distractores: diferentes funciones}
  
  \CommentTok{\# Aleatorizar qué opción (A, B, C, D) será la correcta}
\NormalTok{  letras }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"A"}\NormalTok{, }\StringTok{"B"}\NormalTok{, }\StringTok{"C"}\NormalTok{, }\StringTok{"D"}\NormalTok{)}
\NormalTok{  letra\_correcta }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(letras, }\DecValTok{1}\NormalTok{)}
  
  \CommentTok{\# Crear mapeo de funciones a letras}
\NormalTok{  funciones }\OtherTok{\textless{}{-}} \FunctionTok{list}\NormalTok{(}
    \AttributeTok{correcta =} \StringTok{"cosecante"}\NormalTok{,  }\CommentTok{\# KP = h/sen(α)}
    \AttributeTok{constante =} \StringTok{"constante"}\NormalTok{, }\CommentTok{\# KP = h (línea horizontal)}
    \AttributeTok{lineal =} \StringTok{"lineal"}\NormalTok{,       }\CommentTok{\# KP = función lineal decreciente}
    \AttributeTok{cuadratica =} \StringTok{"cuadratica"} \CommentTok{\# KP = función cuadrática}
\NormalTok{  )}
  
  \CommentTok{\# Asignar funciones a letras aleatoriamente}
\NormalTok{  funciones\_mezcladas }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\FunctionTok{names}\NormalTok{(funciones), }\DecValTok{4}\NormalTok{)}
\NormalTok{  mapeo\_funciones }\OtherTok{\textless{}{-}} \FunctionTok{setNames}\NormalTok{(funciones\_mezcladas, letras)}
  
  \FunctionTok{return}\NormalTok{(}\FunctionTok{list}\NormalTok{(}
    \AttributeTok{h =}\NormalTok{ h,}
    \AttributeTok{punto\_movil =}\NormalTok{ punto\_movil,}
    \AttributeTok{punto\_fijo1 =}\NormalTok{ punto\_fijo1,}
    \AttributeTok{punto\_fijo2 =}\NormalTok{ punto\_fijo2,}
    \AttributeTok{termino\_distancia =}\NormalTok{ termino\_distancia,}
    \AttributeTok{termino\_grafica =}\NormalTok{ termino\_grafica,}
    \AttributeTok{colores\_graficas =}\NormalTok{ colores\_graficas,}
    \AttributeTok{angulo\_min =}\NormalTok{ angulo\_min,}
    \AttributeTok{angulo\_max =}\NormalTok{ angulo\_max,}
    \AttributeTok{letra\_correcta =}\NormalTok{ letra\_correcta,}
    \AttributeTok{mapeo\_funciones =}\NormalTok{ mapeo\_funciones}
\NormalTok{  ))}
\NormalTok{\}}

\CommentTok{\# Generar datos del ejercicio}
\NormalTok{datos }\OtherTok{\textless{}{-}} \FunctionTok{generar\_datos}\NormalTok{()}

\CommentTok{\# Extraer variables individuales}
\NormalTok{h }\OtherTok{\textless{}{-}}\NormalTok{ datos}\SpecialCharTok{$}\NormalTok{h}
\NormalTok{punto\_movil }\OtherTok{\textless{}{-}}\NormalTok{ datos}\SpecialCharTok{$}\NormalTok{punto\_movil}
\NormalTok{punto\_fijo1 }\OtherTok{\textless{}{-}}\NormalTok{ datos}\SpecialCharTok{$}\NormalTok{punto\_fijo1}
\NormalTok{punto\_fijo2 }\OtherTok{\textless{}{-}}\NormalTok{ datos}\SpecialCharTok{$}\NormalTok{punto\_fijo2}
\NormalTok{termino\_distancia }\OtherTok{\textless{}{-}}\NormalTok{ datos}\SpecialCharTok{$}\NormalTok{termino\_distancia}
\NormalTok{termino\_grafica }\OtherTok{\textless{}{-}}\NormalTok{ datos}\SpecialCharTok{$}\NormalTok{termino\_grafica}
\NormalTok{colores\_graficas }\OtherTok{\textless{}{-}}\NormalTok{ datos}\SpecialCharTok{$}\NormalTok{colores\_graficas}
\NormalTok{angulo\_min }\OtherTok{\textless{}{-}}\NormalTok{ datos}\SpecialCharTok{$}\NormalTok{angulo\_min}
\NormalTok{angulo\_max }\OtherTok{\textless{}{-}}\NormalTok{ datos}\SpecialCharTok{$}\NormalTok{angulo\_max}
\NormalTok{letra\_correcta }\OtherTok{\textless{}{-}}\NormalTok{ datos}\SpecialCharTok{$}\NormalTok{letra\_correcta}
\NormalTok{mapeo\_funciones }\OtherTok{\textless{}{-}}\NormalTok{ datos}\SpecialCharTok{$}\NormalTok{mapeo\_funciones}

\CommentTok{\# Crear vector de solución}
\NormalTok{solucion }\OtherTok{\textless{}{-}} \FunctionTok{rep}\NormalTok{(}\DecValTok{0}\NormalTok{, }\DecValTok{4}\NormalTok{)}
\NormalTok{indice\_correcto }\OtherTok{\textless{}{-}} \FunctionTok{which}\NormalTok{(}\FunctionTok{names}\NormalTok{(mapeo\_funciones) }\SpecialCharTok{==}\NormalTok{ letra\_correcta)}
\NormalTok{solucion[indice\_correcto] }\OtherTok{\textless{}{-}} \DecValTok{1}
\end{Highlighting}
\end{Shaded}

\section{Question}\label{question}

Un punto R se mueve de un extremo a otro del segmento ST que se muestra
en la gráfica.

\includegraphics[width=12cm,height=\textheight,keepaspectratio]{diagrama_trigonometrico.png}

¿Cuál de las siguientes gráficas representa mejor la relación entre la
distancia RP y el ángulo α?

\subsection{Answerlist}\label{answerlist}

\begin{itemize}
\item
  \includegraphics[width=0.7\linewidth,height=\textheight,keepaspectratio]{grafica_a.png}
\item
  \includegraphics[width=0.7\linewidth,height=\textheight,keepaspectratio]{grafica_b.png}
\item
  \includegraphics[width=0.7\linewidth,height=\textheight,keepaspectratio]{grafica_c.png}
\item
  \includegraphics[width=0.7\linewidth,height=\textheight,keepaspectratio]{grafica_d.png}
\end{itemize}

\section{Solution}\label{solution}

Para resolver este problema, debemos analizar la relación trigonométrica
dada y determinar cómo se comporta la función RP = h/sen(α) cuando el
ángulo α varía.

\subsubsection{Paso 1: Análisis de la relación
trigonométrica}\label{paso-1-anuxe1lisis-de-la-relaciuxf3n-trigonomuxe9trica}

La relación dada es: \[\sin(\alpha) = \frac{h}{RP}\]

Despejando RP: \[RP = \frac{h}{\sin(\alpha)} = h \times \csc(\alpha)\]

Donde: - h es una constante (altura fija = 6) - α es el ángulo variable
- RP es la distancia que queremos analizar

\subsubsection{Paso 2: Comportamiento de la función
cosecante}\label{paso-2-comportamiento-de-la-funciuxf3n-cosecante}

La función cosecante tiene las siguientes características importantes:

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\tightlist
\item
  \textbf{Cuando α → 0°}: sin(α) → 0, por lo tanto RP = h/sin(α) → ∞
\item
  \textbf{Cuando α aumenta}: sin(α) aumenta, por lo tanto RP = h/sin(α)
  disminuye
\item
  \textbf{Cuando α → 90°}: sin(α) → 1, por lo tanto RP = h/sin(α) → h =
  6
\end{enumerate}

\subsubsection{Paso 3: Características de la gráfica
correcta}\label{paso-3-caracteruxedsticas-de-la-gruxe1fica-correcta}

La gráfica que representa correctamente RP = h/sin(α) debe mostrar:

\begin{itemize}
\tightlist
\item
  \textbf{Función decreciente}: A medida que α aumenta, RP disminuye
\item
  \textbf{Comportamiento asintótico}: Para ángulos pequeños, RP tiende a
  valores muy grandes
\item
  \textbf{Valor mínimo}: Cuando α se acerca a 90°, RP se acerca a h = 6
\item
  \textbf{Forma de cosecante}: Curva suave y decreciente, no lineal
\end{itemize}

\subsubsection{Paso 4: Análisis de las
opciones}\label{paso-4-anuxe1lisis-de-las-opciones}

\textbf{La gráfica correcta es la opción C :}

\includegraphics[width=0.7\linewidth,height=\textheight,keepaspectratio]{grafica_c.png}

Esta gráfica es correcta porque:

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\tightlist
\item
  \textbf{Muestra una función decreciente}: Conforme α aumenta, RP
  disminuye
\item
  \textbf{Tiene la forma característica de la cosecante}: Curva suave
  que decrece rápidamente al inicio y luego se estabiliza
\item
  \textbf{Se aproxima al valor mínimo h}: Para ángulos cercanos a 90°,
  RP se acerca a 6
\item
  \textbf{Es matemáticamente consistente}: Representa fielmente la
  función RP = 6/sin(α)
\end{enumerate}

\subsubsection{Paso 5: ¿Por qué las otras opciones son
incorrectas?}\label{paso-5-por-quuxe9-las-otras-opciones-son-incorrectas}

\begin{itemize}
\tightlist
\item
  \textbf{Gráficas horizontales constantes}: No representan la variación
  de RP con respecto a α
\item
  \textbf{Funciones lineales}: La relación cosecante no es lineal
\item
  \textbf{Funciones con comportamiento incorrecto}: No siguen el patrón
  matemático de h/sen(α)
\end{itemize}

\subsubsection{Conclusión}\label{conclusiuxf3n}

La función RP = h/sin(α) = h × csc(α) es una función cosecante que
decrece desde valores muy altos (cuando α es pequeño) hasta el valor
mínimo h (cuando α se acerca a 90°). Solo la opción \textbf{C}
representa correctamente este comportamiento matemático.

\subsection{Answerlist}\label{answerlist-1}

\begin{itemize}
\tightlist
\item
  Falso
\item
  Falso
\item
  Verdadero
\item
  Falso
\end{itemize}

\section{Meta-information}\label{meta-information}

exname:
trigonometria\_funcion\_cosecante\_interpretacion\_representacion\_n2\_v1
extype: schoice exsolution: 0010 exshuffle: TRUE exsection:
Trigonometría\textbar Funciones trigonométricas\textbar Interpretación
de gráficos exextra{[}Type{]}: Interpretación y representación
exextra{[}Level{]}: 2 exextra{[}Language{]}: es exextra{[}Course{]}:
Matemáticas ICFES

\end{document}
